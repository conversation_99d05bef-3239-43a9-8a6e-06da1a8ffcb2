import Foundation
import CoreMotion
import UIKit

/// 设备运动管理器
/// 负责获取和处理设备的陀螺仪、加速度计等运动数据
class DeviceMotionManager: ObservableObject {

    // MARK: - 发布属性

    /// 当前设备姿态数据
    @Published var deviceAttitude: CMAttitude?

    /// 当前重力向量（在设备坐标系中）
    @Published var gravityVector: CMAcceleration?

    /// 设备运动管理器是否可用
    @Published var isAvailable: Bool = false

    /// 设备运动管理器是否正在运行
    @Published var isActive: Bool = false

    // MARK: - 私有属性

    /// CoreMotion 管理器
    private let motionManager = CMMotionManager()

    /// 运动数据更新队列
    private let motionQueue = OperationQueue()

    /// 更新频率（Hz）
    private let updateInterval: TimeInterval = 1.0 / 60.0 // 60 FPS

    /// 重力向量平滑滤波器
    private var gravityFilter = LowPassFilter(alpha: 0.1)

    // MARK: - 初始化

    init() {
        setupMotionManager()
        checkAvailability()
    }

    deinit {
        stopMotionUpdates()
    }

    // MARK: - 公共方法

    /// 开始运动数据更新
    func startMotionUpdates() {
        guard motionManager.isDeviceMotionAvailable && !isActive else {
            DebugLogger.warning("设备运动数据不可用或已经在运行")
            return
        }

        DebugLogger.info("开始设备运动数据更新")

        // 选择最佳的参考坐标系
        let referenceFrame: CMAttitudeReferenceFrame
        if CMMotionManager.availableAttitudeReferenceFrames().contains(.xMagneticNorthZVertical) {
            referenceFrame = .xMagneticNorthZVertical
        } else if CMMotionManager.availableAttitudeReferenceFrames().contains(.xArbitraryZVertical) {
            referenceFrame = .xArbitraryZVertical
        } else {
            referenceFrame = .xArbitraryZVertical // 默认值
        }

        motionManager.startDeviceMotionUpdates(using: referenceFrame, to: motionQueue) { [weak self] (motion, error) in
            guard let self = self, let motion = motion else {
                if let error = error {
                    DebugLogger.error("设备运动数据更新错误: \(error.localizedDescription)")
                }
                return
            }

            self.processMotionData(motion)
        }

        DispatchQueue.main.async { [weak self] in
            self?.isActive = true
        }
    }

    /// 停止运动数据更新
    func stopMotionUpdates() {
        guard isActive else { return }

        DebugLogger.info("停止设备运动数据更新")
        motionManager.stopDeviceMotionUpdates()

        DispatchQueue.main.async { [weak self] in
            self?.isActive = false
        }
    }

    /// 获取当前的精确重力向量（在 MediaPipe worldLandmarks 坐标系中）
    /// - Returns: 重力向量的 3D 坐标，如果数据不可用则返回 nil
    ///
    /// 重要说明：返回的重力向量已转换为 MediaPipe worldLandmarks 坐标系，
    /// 该坐标系是固定的世界坐标系，不受设备方向影响。
    /// 这确保了相同身体姿势在不同设备方向下计算出一致的角度。
    func getPreciseGravityVector() -> (x: Float, y: Float, z: Float)? {
        guard let gravity = gravityVector else {
            DebugLogger.warning("重力向量数据不可用")
            return nil
        }

        // 将 CoreMotion 重力向量转换为 MediaPipe worldLandmarks 坐标系
        //
        // CoreMotion 坐标系（设备坐标系）：
        // - X轴：设备右侧为正
        // - Y轴：设备顶部为正
        // - Z轴：设备屏幕向外为正
        //
        // MediaPipe worldLandmarks 坐标系（世界坐标系，以人体为中心）：
        // - X轴：人体左右方向（正值向人体右侧）
        // - Y轴：人体上下方向（正值向人体上方）
        // - Z轴：人体前后方向（正值向人体前方）
        //
        // 关键：MediaPipe worldLandmarks 坐标系不受设备方向影响，
        // 因此我们需要将设备坐标系的重力向量转换为世界坐标系

        let convertedGravity = convertGravityToWorldCoordinates(gravity: gravity)

        DebugLogger.debug("CoreMotion 重力向量: (\(gravity.x), \(gravity.y), \(gravity.z))")
        DebugLogger.debug("转换后世界坐标系重力向量: (\(convertedGravity.x), \(convertedGravity.y), \(convertedGravity.z))")

        return convertedGravity
    }

    /// 获取设备当前的倾斜角度信息
    /// - Returns: 包含 pitch、roll、yaw 的元组
    func getDeviceAngles() -> (pitch: Double, roll: Double, yaw: Double)? {
        guard let attitude = deviceAttitude else { return nil }

        return (
            pitch: attitude.pitch * 180.0 / .pi,  // 俯仰角（度）
            roll: attitude.roll * 180.0 / .pi,    // 翻滚角（度）
            yaw: attitude.yaw * 180.0 / .pi       // 偏航角（度）
        )
    }

    // MARK: - 私有方法

    /// 设置运动管理器
    private func setupMotionManager() {
        motionQueue.name = "DeviceMotionQueue"
        motionQueue.maxConcurrentOperationCount = 1

        motionManager.deviceMotionUpdateInterval = updateInterval

        // 注意：attitudeReferenceFrame 是只读属性，在 startDeviceMotionUpdates 时设置

        DebugLogger.debug("设备运动管理器配置完成，更新频率: \(1.0/updateInterval) Hz")
    }

    /// 检查设备运动功能可用性
    private func checkAvailability() {
        let available = motionManager.isDeviceMotionAvailable

        DispatchQueue.main.async { [weak self] in
            self?.isAvailable = available
        }

        if available {
            DebugLogger.info(NSLocalizedString("device_motion_data_available", comment: "设备运动数据可用"))
        } else {
            DebugLogger.warning(NSLocalizedString("device_motion_data_unavailable", comment: "设备运动数据不可用"))
        }
    }

    /// 处理运动数据
    /// - Parameter motion: CoreMotion 设备运动数据
    private func processMotionData(_ motion: CMDeviceMotion) {
        // 获取重力向量并应用低通滤波
        let rawGravity = motion.gravity
        let filteredGravity = gravityFilter.filter(rawGravity)

        // 获取设备姿态
        let attitude = motion.attitude

        // 在主线程更新发布属性
        DispatchQueue.main.async { [weak self] in
            self?.gravityVector = filteredGravity
            self?.deviceAttitude = attitude
        }

        // 调试输出（降低频率）
        if Int(Date().timeIntervalSince1970 * 10) % 10 == 0 { // 每秒输出一次
            let angles = (
                pitch: attitude.pitch * 180.0 / .pi,
                roll: attitude.roll * 180.0 / .pi,
                yaw: attitude.yaw * 180.0 / .pi
            )

            DebugLogger.debug("设备姿态 - Pitch: \(String(format: "%.1f", angles.pitch))°, Roll: \(String(format: "%.1f", angles.roll))°, Yaw: \(String(format: "%.1f", angles.yaw))°")
            DebugLogger.debug("重力向量 - X: \(String(format: "%.3f", filteredGravity.x)), Y: \(String(format: "%.3f", filteredGravity.y)), Z: \(String(format: "%.3f", filteredGravity.z))")
        }
    }

    /// 将 CoreMotion 重力向量转换为 MediaPipe worldLandmarks 坐标系
    /// - Parameter gravity: CoreMotion 重力向量
    /// - Returns: MediaPipe worldLandmarks 坐标系中的重力向量
    ///
    /// 坐标系转换说明：
    /// CoreMotion 坐标系（设备坐标系）：
    /// - X轴：设备右侧为正
    /// - Y轴：设备顶部为正
    /// - Z轴：设备屏幕向外为正
    ///
    /// MediaPipe worldLandmarks 坐标系（基于图像方向）：
    /// - 坐标系会根据图像的实际方向而变化
    /// - 需要根据当前设备方向和界面方向正确转换重力向量
    ///
    /// 关键：确保在所有设备方向和画面旋转状态下，相同的身体姿势计算出一致的角度
    private func convertGravityToWorldCoordinates(
        gravity: CMAcceleration
    ) -> (x: Float, y: Float, z: Float) {

        // 记录原始重力向量
        DebugLogger.debug("原始 CoreMotion 重力向量: (\(String(format: "%.3f", gravity.x)), \(String(format: "%.3f", gravity.y)), \(String(format: "%.3f", gravity.z)))")

        // 计算重力向量的长度
        let magnitude = sqrt(gravity.x * gravity.x + gravity.y * gravity.y + gravity.z * gravity.z)
        DebugLogger.debug("重力向量长度: \(String(format: "%.3f", magnitude))")

        // 检查重力向量是否有效
        guard magnitude > 0.1 else {
            DebugLogger.warning("CoreMotion 重力向量长度过小 (\(magnitude))，使用标准重力向量")
            return (x: 0.0, y: -1.0, z: 0.0)
        }

        // 获取当前设备物理方向和界面方向
        let deviceOrientation = UIDevice.current.orientation
        let interfaceOrientation = getInterfaceOrientation()

        DebugLogger.debug("当前设备物理方向: \(deviceOrientation.rawValue) (\(orientationDescription(deviceOrientation)))")
        DebugLogger.debug("当前界面显示方向: \(interfaceOrientation.rawValue) (\(interfaceOrientationDescription(interfaceOrientation)))")

        // 检查画面旋转状态
        let isAutoRotationEnabled = isInterfaceAutoRotationEnabled()
        DebugLogger.debug("画面自动旋转状态: \(isAutoRotationEnabled ? "开启" : "关闭")")

        // 核心修复逻辑：
        //
        // 问题分析：
        // 1. 当画面旋转关闭时：界面方向固定，MediaPipe 坐标系相对稳定，使用设备物理方向转换
        // 2. 当画面旋转开启时：界面会跟随设备旋转，MediaPipe 坐标系也会变化，需要使用界面方向转换
        //
        // 解决方案：
        // - 画面旋转关闭：使用设备物理方向 (deviceOrientation) 进行转换
        // - 画面旋转开启：使用界面显示方向 (interfaceOrientation) 进行转换

        let orientationForConversion: UIDeviceOrientation

        if isAutoRotationEnabled {
            // 画面旋转开启：使用界面方向进行转换
            // 因为此时 MediaPipe 处理的图像方向会跟随界面旋转
            orientationForConversion = interfaceOrientationToDeviceOrientation(interfaceOrientation)
            DebugLogger.debug("🔄 画面旋转开启，使用界面方向进行转换: \(orientationDescription(orientationForConversion))")
        } else {
            // 画面旋转关闭：使用设备物理方向进行转换
            // 因为此时 MediaPipe 处理的图像方向相对固定
            orientationForConversion = deviceOrientation
            DebugLogger.debug("🔒 画面旋转关闭，使用设备物理方向进行转换: \(orientationDescription(orientationForConversion))")
        }

        let convertedGravity: (x: Double, y: Double, z: Double)

        // 根据确定的方向进行正确的坐标转换
        switch orientationForConversion {
        case .portrait:
            // 竖屏：重力指向图像下方（Y轴负方向）
            convertedGravity = (x: gravity.x, y: -gravity.y, z: gravity.z)
            DebugLogger.debug("📱 竖屏模式：重力向量转换")

        case .portraitUpsideDown:
            // 倒置竖屏：重力指向图像上方（Y轴正方向）
            convertedGravity = (x: -gravity.x, y: gravity.y, z: gravity.z)
            DebugLogger.debug("🙃 倒置竖屏模式：重力向量转换")

        case .landscapeLeft:
            // 左横屏：设备逆时针旋转90度
            // 原来的Y轴（设备顶部）现在指向图像右侧（X轴正方向）
            // 原来的X轴（设备右侧）现在指向图像下方（Y轴负方向）
            convertedGravity = (x: gravity.y, y: gravity.x, z: gravity.z)
            DebugLogger.debug("↪️ 左横屏模式：重力向量转换")

        case .landscapeRight:
            // 右横屏：设备顺时针旋转90度
            // 原来的Y轴（设备顶部）现在指向图像左侧（X轴负方向）
            // 原来的X轴（设备右侧）现在指向图像上方（Y轴正方向）
            convertedGravity = (x: -gravity.y, y: -gravity.x, z: gravity.z)
            DebugLogger.debug("↩️ 右横屏模式：重力向量转换")

        default:
            // 对于其他方向（如 faceUp, faceDown, unknown），使用竖屏的转换
            DebugLogger.warning("⚠️ 未知或不支持的方向 \(orientationForConversion)，使用竖屏转换")
            convertedGravity = (x: gravity.x, y: -gravity.y, z: gravity.z)
        }

        // 标准化重力向量
        let convertedMagnitude = sqrt(
            convertedGravity.x * convertedGravity.x +
            convertedGravity.y * convertedGravity.y +
            convertedGravity.z * convertedGravity.z
        )

        let normalizedGravity = (
            x: Float(convertedGravity.x / convertedMagnitude),
            y: Float(convertedGravity.y / convertedMagnitude),
            z: Float(convertedGravity.z / convertedMagnitude)
        )

        DebugLogger.debug("转换后重力向量: (\(String(format: "%.3f", normalizedGravity.x)), \(String(format: "%.3f", normalizedGravity.y)), \(String(format: "%.3f", normalizedGravity.z)))")

        // 验证转换后的重力向量长度
        let finalMagnitude = sqrt(normalizedGravity.x * normalizedGravity.x + normalizedGravity.y * normalizedGravity.y + normalizedGravity.z * normalizedGravity.z)
        DebugLogger.debug("转换后重力向量长度: \(String(format: "%.3f", finalMagnitude))")

        return normalizedGravity
    }

    /// 获取设备方向的描述文本
    /// - Parameter orientation: 设备方向
    /// - Returns: 方向描述文本
    private func orientationDescription(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "面朝上"
        case .faceDown:
            return "面朝下"
        case .unknown:
            return "未知"
        @unknown default:
            return "未知"
        }
    }

    /// 获取界面方向的描述文本
    /// - Parameter orientation: 界面方向
    /// - Returns: 方向描述文本
    private func interfaceOrientationDescription(_ orientation: UIInterfaceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "界面竖屏"
        case .portraitUpsideDown:
            return "界面倒置竖屏"
        case .landscapeLeft:
            return "界面左横屏"
        case .landscapeRight:
            return "界面右横屏"
        case .unknown:
            return "界面未知"
        @unknown default:
            return "界面未知"
        }
    }

    /// 获取当前界面方向
    /// - Returns: 当前界面方向
    private func getInterfaceOrientation() -> UIInterfaceOrientation {
        // 在 iOS 13+ 中，需要通过 window scene 获取界面方向
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            return windowScene.interfaceOrientation
        }

        // 降级处理：如果无法获取 window scene，返回未知
        DebugLogger.warning("无法获取界面方向，返回未知")
        return .unknown
    }

    /// 检查界面自动旋转是否开启
    /// - Returns: 是否开启自动旋转
    private func isInterfaceAutoRotationEnabled() -> Bool {
        // 检查应用级别的自动旋转设置
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            DebugLogger.warning("无法获取根视图控制器，假设自动旋转开启")
            return true
        }

        // 检查当前视图控制器是否支持自动旋转
        let shouldAutorotate = rootViewController.shouldAutorotate

        // 检查系统级别的旋转锁定状态
        // 注意：iOS 没有直接的 API 来检查控制中心的旋转锁定状态
        // 我们通过比较设备方向和界面方向来推断
        let deviceOrientation = UIDevice.current.orientation
        let interfaceOrientation = getInterfaceOrientation()

        // 如果设备方向和界面方向不一致，且视图控制器支持自动旋转，
        // 则可能是旋转锁定开启了
        let orientationsMatch = deviceOrientationMatchesInterface(deviceOrientation, interfaceOrientation)

        DebugLogger.debug("视图控制器支持自动旋转: \(shouldAutorotate)")
        DebugLogger.debug("设备方向与界面方向匹配: \(orientationsMatch)")

        return shouldAutorotate && orientationsMatch
    }

    /// 检查设备方向是否与界面方向匹配
    /// - Parameters:
    ///   - deviceOrientation: 设备物理方向
    ///   - interfaceOrientation: 界面显示方向
    /// - Returns: 是否匹配
    private func deviceOrientationMatchesInterface(
        _ deviceOrientation: UIDeviceOrientation,
        _ interfaceOrientation: UIInterfaceOrientation
    ) -> Bool {
        switch (deviceOrientation, interfaceOrientation) {
        case (.portrait, .portrait),
             (.portraitUpsideDown, .portraitUpsideDown),
             (.landscapeLeft, .landscapeRight),  // 注意：设备左横屏对应界面右横屏
             (.landscapeRight, .landscapeLeft):  // 注意：设备右横屏对应界面左横屏
            return true
        default:
            return false
        }
    }

    /// 将界面方向转换为设备方向
    /// - Parameter interfaceOrientation: 界面方向
    /// - Returns: 对应的设备方向
    private func interfaceOrientationToDeviceOrientation(
        _ interfaceOrientation: UIInterfaceOrientation
    ) -> UIDeviceOrientation {
        switch interfaceOrientation {
        case .portrait:
            return .portrait
        case .portraitUpsideDown:
            return .portraitUpsideDown
        case .landscapeLeft:
            return .landscapeRight  // 界面左横屏对应设备右横屏
        case .landscapeRight:
            return .landscapeLeft   // 界面右横屏对应设备左横屏
        case .unknown:
            return .unknown
        @unknown default:
            return .unknown
        }
    }
}

// MARK: - 低通滤波器

/// 低通滤波器，用于平滑传感器数据
private struct LowPassFilter {
    private let alpha: Double
    private var previousValue: CMAcceleration?

    init(alpha: Double) {
        self.alpha = alpha
    }

    mutating func filter(_ newValue: CMAcceleration) -> CMAcceleration {
        guard let previous = previousValue else {
            previousValue = newValue
            return newValue
        }

        let filtered = CMAcceleration(
            x: alpha * newValue.x + (1.0 - alpha) * previous.x,
            y: alpha * newValue.y + (1.0 - alpha) * previous.y,
            z: alpha * newValue.z + (1.0 - alpha) * previous.z
        )

        previousValue = filtered
        return filtered
    }
}
