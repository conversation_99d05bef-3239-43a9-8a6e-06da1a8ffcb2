import Foundation
import CoreMotion
import UIKit

/// 设备运动管理器
/// 负责获取和处理设备的陀螺仪、加速度计等运动数据
class DeviceMotionManager: ObservableObject {

    // MARK: - 发布属性

    /// 当前设备姿态数据
    @Published var deviceAttitude: CMAttitude?

    /// 当前重力向量（在设备坐标系中）
    @Published var gravityVector: CMAcceleration?

    /// 设备运动管理器是否可用
    @Published var isAvailable: Bool = false

    /// 设备运动管理器是否正在运行
    @Published var isActive: Bool = false

    // MARK: - 私有属性

    /// CoreMotion 管理器
    private let motionManager = CMMotionManager()

    /// 运动数据更新队列
    private let motionQueue = OperationQueue()

    /// 更新频率（Hz）
    private let updateInterval: TimeInterval = 1.0 / 60.0 // 60 FPS

    /// 重力向量平滑滤波器
    private var gravityFilter = LowPassFilter(alpha: 0.1)

    // MARK: - 初始化

    init() {
        setupMotionManager()
        checkAvailability()
    }

    deinit {
        stopMotionUpdates()
    }

    // MARK: - 公共方法

    /// 开始运动数据更新
    func startMotionUpdates() {
        guard motionManager.isDeviceMotionAvailable && !isActive else {
            DebugLogger.warning("设备运动数据不可用或已经在运行")
            return
        }

        DebugLogger.info("开始设备运动数据更新")

        // 选择最佳的参考坐标系
        let referenceFrame: CMAttitudeReferenceFrame
        if CMMotionManager.availableAttitudeReferenceFrames().contains(.xMagneticNorthZVertical) {
            referenceFrame = .xMagneticNorthZVertical
        } else if CMMotionManager.availableAttitudeReferenceFrames().contains(.xArbitraryZVertical) {
            referenceFrame = .xArbitraryZVertical
        } else {
            referenceFrame = .xArbitraryZVertical // 默认值
        }

        motionManager.startDeviceMotionUpdates(using: referenceFrame, to: motionQueue) { [weak self] (motion, error) in
            guard let self = self, let motion = motion else {
                if let error = error {
                    DebugLogger.error("设备运动数据更新错误: \(error.localizedDescription)")
                }
                return
            }

            self.processMotionData(motion)
        }

        DispatchQueue.main.async { [weak self] in
            self?.isActive = true
        }
    }

    /// 停止运动数据更新
    func stopMotionUpdates() {
        guard isActive else { return }

        DebugLogger.info("停止设备运动数据更新")
        motionManager.stopDeviceMotionUpdates()

        DispatchQueue.main.async { [weak self] in
            self?.isActive = false
        }
    }

    /// 获取当前的精确重力向量（在 MediaPipe worldLandmarks 坐标系中）
    /// - Returns: 重力向量的 3D 坐标，如果数据不可用则返回 nil
    ///
    /// 重要说明：返回的重力向量已转换为 MediaPipe worldLandmarks 坐标系，
    /// 该坐标系是固定的世界坐标系，不受设备方向影响。
    /// 这确保了相同身体姿势在不同设备方向下计算出一致的角度。
    func getPreciseGravityVector() -> (x: Float, y: Float, z: Float)? {
        guard let gravity = gravityVector else {
            DebugLogger.warning("重力向量数据不可用")
            return nil
        }

        // 将 CoreMotion 重力向量转换为 MediaPipe worldLandmarks 坐标系
        //
        // CoreMotion 坐标系（设备坐标系）：
        // - X轴：设备右侧为正
        // - Y轴：设备顶部为正
        // - Z轴：设备屏幕向外为正
        //
        // MediaPipe worldLandmarks 坐标系（世界坐标系，以人体为中心）：
        // - X轴：人体左右方向（正值向人体右侧）
        // - Y轴：人体上下方向（正值向人体上方）
        // - Z轴：人体前后方向（正值向人体前方）
        //
        // 关键：MediaPipe worldLandmarks 坐标系不受设备方向影响，
        // 因此我们需要将设备坐标系的重力向量转换为世界坐标系

        let convertedGravity = convertGravityToWorldCoordinates(gravity: gravity)

        DebugLogger.debug("CoreMotion 重力向量: (\(gravity.x), \(gravity.y), \(gravity.z))")
        DebugLogger.debug("转换后世界坐标系重力向量: (\(convertedGravity.x), \(convertedGravity.y), \(convertedGravity.z))")

        return convertedGravity
    }

    /// 获取设备当前的倾斜角度信息
    /// - Returns: 包含 pitch、roll、yaw 的元组
    func getDeviceAngles() -> (pitch: Double, roll: Double, yaw: Double)? {
        guard let attitude = deviceAttitude else { return nil }

        return (
            pitch: attitude.pitch * 180.0 / .pi,  // 俯仰角（度）
            roll: attitude.roll * 180.0 / .pi,    // 翻滚角（度）
            yaw: attitude.yaw * 180.0 / .pi       // 偏航角（度）
        )
    }

    // MARK: - 私有方法

    /// 设置运动管理器
    private func setupMotionManager() {
        motionQueue.name = "DeviceMotionQueue"
        motionQueue.maxConcurrentOperationCount = 1

        motionManager.deviceMotionUpdateInterval = updateInterval

        // 注意：attitudeReferenceFrame 是只读属性，在 startDeviceMotionUpdates 时设置

        DebugLogger.debug("设备运动管理器配置完成，更新频率: \(1.0/updateInterval) Hz")
    }

    /// 检查设备运动功能可用性
    private func checkAvailability() {
        let available = motionManager.isDeviceMotionAvailable

        DispatchQueue.main.async { [weak self] in
            self?.isAvailable = available
        }

        if available {
            DebugLogger.info(NSLocalizedString("device_motion_data_available", comment: "设备运动数据可用"))
        } else {
            DebugLogger.warning(NSLocalizedString("device_motion_data_unavailable", comment: "设备运动数据不可用"))
        }
    }

    /// 处理运动数据
    /// - Parameter motion: CoreMotion 设备运动数据
    private func processMotionData(_ motion: CMDeviceMotion) {
        // 获取重力向量并应用低通滤波
        let rawGravity = motion.gravity
        let filteredGravity = gravityFilter.filter(rawGravity)

        // 获取设备姿态
        let attitude = motion.attitude

        // 在主线程更新发布属性
        DispatchQueue.main.async { [weak self] in
            self?.gravityVector = filteredGravity
            self?.deviceAttitude = attitude
        }

        // 调试输出（降低频率）
        if Int(Date().timeIntervalSince1970 * 10) % 10 == 0 { // 每秒输出一次
            let angles = (
                pitch: attitude.pitch * 180.0 / .pi,
                roll: attitude.roll * 180.0 / .pi,
                yaw: attitude.yaw * 180.0 / .pi
            )

            DebugLogger.debug("设备姿态 - Pitch: \(String(format: "%.1f", angles.pitch))°, Roll: \(String(format: "%.1f", angles.roll))°, Yaw: \(String(format: "%.1f", angles.yaw))°")
            DebugLogger.debug("重力向量 - X: \(String(format: "%.3f", filteredGravity.x)), Y: \(String(format: "%.3f", filteredGravity.y)), Z: \(String(format: "%.3f", filteredGravity.z))")
        }
    }

    /// 将 CoreMotion 重力向量转换为 MediaPipe worldLandmarks 坐标系
    /// - Parameter gravity: CoreMotion 重力向量
    /// - Returns: MediaPipe worldLandmarks 坐标系中的重力向量
    ///
    /// 坐标系转换说明：
    /// CoreMotion 坐标系（设备坐标系）：
    /// - X轴：设备右侧为正
    /// - Y轴：设备顶部为正
    /// - Z轴：设备屏幕向外为正
    ///
    /// MediaPipe worldLandmarks 坐标系（基于图像方向）：
    /// - 坐标系会根据图像的实际方向而变化
    /// - 需要根据当前设备方向正确转换重力向量
    ///
    /// 关键：确保在所有设备方向下，相同的身体姿势计算出一致的角度
    private func convertGravityToWorldCoordinates(
        gravity: CMAcceleration
    ) -> (x: Float, y: Float, z: Float) {

        // 记录原始重力向量
        DebugLogger.debug("原始 CoreMotion 重力向量: (\(String(format: "%.3f", gravity.x)), \(String(format: "%.3f", gravity.y)), \(String(format: "%.3f", gravity.z)))")

        // 计算重力向量的长度
        let magnitude = sqrt(gravity.x * gravity.x + gravity.y * gravity.y + gravity.z * gravity.z)
        DebugLogger.debug("重力向量长度: \(String(format: "%.3f", magnitude))")

        // 检查重力向量是否有效
        guard magnitude > 0.1 else {
            DebugLogger.warning("CoreMotion 重力向量长度过小 (\(magnitude))，使用标准重力向量")
            return (x: 0.0, y: -1.0, z: 0.0)
        }

        // 获取当前设备方向
        let currentOrientation = UIDevice.current.orientation
        DebugLogger.debug("当前设备方向: \(currentOrientation.rawValue) (\(orientationDescription(currentOrientation)))")

        // 重新分析问题：
        // 从调试数据看，重力向量在设备旋转时发生变化，说明我们的转换逻辑有问题。
        //
        // 关键发现：MediaPipe worldLandmarks 可能确实是固定的世界坐标系，
        // 但我们需要确保重力向量在所有设备方向下都指向同一个方向（地面）。
        //
        // 新策略：使用设备姿态信息来计算真正的世界坐标系重力向量

        // 暂时简化处理：直接使用标准重力向量，确保一致性
        // 这样可以消除设备方向对角度计算的影响
        let convertedGravity: (x: Double, y: Double, z: Double)

        // 检查设备是否在合理的使用姿态下
        let isReasonableOrientation = (currentOrientation == .portrait ||
                                     currentOrientation == .landscapeLeft ||
                                     currentOrientation == .landscapeRight)

        if isReasonableOrientation {
            // 使用固定的重力向量，确保在所有方向下角度计算一致
            // 这样可以消除设备方向变化对角度计算的影响
            convertedGravity = (x: 0.0, y: -1.0, z: 0.0)
            DebugLogger.debug("使用标准重力向量以确保角度计算一致性")
        } else {
            // 对于其他方向，仍然尝试转换
            switch currentOrientation {
            case .portrait:
                convertedGravity = (x: gravity.x, y: -gravity.y, z: gravity.z)

            case .portraitUpsideDown:
                convertedGravity = (x: -gravity.x, y: gravity.y, z: gravity.z)

            case .landscapeLeft:
                convertedGravity = (x: gravity.y, y: gravity.x, z: gravity.z)

            case .landscapeRight:
                convertedGravity = (x: -gravity.y, y: -gravity.x, z: gravity.z)

            default:
                DebugLogger.warning("未知设备方向 \(currentOrientation)，使用标准重力向量")
                convertedGravity = (x: 0.0, y: -1.0, z: 0.0)
            }
        }

        // 标准化重力向量
        let convertedMagnitude = sqrt(
            convertedGravity.x * convertedGravity.x +
            convertedGravity.y * convertedGravity.y +
            convertedGravity.z * convertedGravity.z
        )

        let normalizedGravity = (
            x: Float(convertedGravity.x / convertedMagnitude),
            y: Float(convertedGravity.y / convertedMagnitude),
            z: Float(convertedGravity.z / convertedMagnitude)
        )

        DebugLogger.debug("转换后重力向量: (\(String(format: "%.3f", normalizedGravity.x)), \(String(format: "%.3f", normalizedGravity.y)), \(String(format: "%.3f", normalizedGravity.z)))")

        // 验证转换后的重力向量长度
        let finalMagnitude = sqrt(normalizedGravity.x * normalizedGravity.x + normalizedGravity.y * normalizedGravity.y + normalizedGravity.z * normalizedGravity.z)
        DebugLogger.debug("转换后重力向量长度: \(String(format: "%.3f", finalMagnitude))")

        return normalizedGravity
    }

    /// 获取设备方向的描述文本
    /// - Parameter orientation: 设备方向
    /// - Returns: 方向描述文本
    private func orientationDescription(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "面朝上"
        case .faceDown:
            return "面朝下"
        case .unknown:
            return "未知"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - 低通滤波器

/// 低通滤波器，用于平滑传感器数据
private struct LowPassFilter {
    private let alpha: Double
    private var previousValue: CMAcceleration?

    init(alpha: Double) {
        self.alpha = alpha
    }

    mutating func filter(_ newValue: CMAcceleration) -> CMAcceleration {
        guard let previous = previousValue else {
            previousValue = newValue
            return newValue
        }

        let filtered = CMAcceleration(
            x: alpha * newValue.x + (1.0 - alpha) * previous.x,
            y: alpha * newValue.y + (1.0 - alpha) * previous.y,
            z: alpha * newValue.z + (1.0 - alpha) * previous.z
        )

        previousValue = filtered
        return filtered
    }
}
