# FitCount 屏幕旋转测试指南

## 测试目的

验证 FitCount iOS 项目中角度计算在不同屏幕旋转和画面旋转状态下的一致性。

## 测试前准备

### 1. 设备要求
- **必须使用真实 iOS 设备**（iPhone 或 iPad）
- 模拟器无法提供准确的重力数据和旋转状态

### 2. 应用设置
- 确保应用已安装并可正常运行
- 确保相机权限已授予
- 确保设备运动权限已授予

### 3. 测试环境
- 选择光线充足的环境进行人体姿态检测
- 准备一个稳定的身体姿势（建议：站立姿势）

## 测试步骤

### 测试场景 1：画面旋转关闭状态

#### 步骤 1：设置画面旋转锁定
1. 从屏幕右上角向下滑动，打开控制中心
2. 点击旋转锁定按钮（🔒图标），确保画面旋转被锁定
3. 确认状态栏显示旋转锁定图标

#### 步骤 2：竖屏测试
1. 将设备保持竖屏方向
2. 打开 FitCount 应用
3. 进入仰卧起坐检测界面
4. 保持站立姿势，观察角度计算结果
5. 记录显示的角度值（应该约为 85-90°）

#### 步骤 3：左横屏测试
1. 保持相同的站立姿势
2. 将设备逆时针旋转 90° 至左横屏
3. 观察角度计算结果
4. 记录显示的角度值（应该与竖屏时一致）

#### 步骤 4：右横屏测试
1. 保持相同的站立姿势
2. 将设备顺时针旋转 90° 至右横屏
3. 观察角度计算结果
4. 记录显示的角度值（应该与竖屏时一致）

### 测试场景 2：画面旋转开启状态

#### 步骤 1：取消画面旋转锁定
1. 从屏幕右上角向下滑动，打开控制中心
2. 点击旋转锁定按钮，取消旋转锁定
3. 确认状态栏不再显示旋转锁定图标

#### 步骤 2：竖屏测试
1. 将设备保持竖屏方向
2. 确认应用界面为竖屏显示
3. 保持站立姿势，观察角度计算结果
4. 记录显示的角度值

#### 步骤 3：左横屏测试
1. 保持相同的站立姿势
2. 将设备逆时针旋转 90° 至左横屏
3. 等待应用界面自动旋转至横屏
4. 观察角度计算结果
5. 记录显示的角度值（应该与竖屏时一致）

#### 步骤 4：右横屏测试
1. 保持相同的站立姿势
2. 将设备顺时针旋转 90° 至右横屏
3. 等待应用界面自动旋转至横屏
4. 观察角度计算结果
5. 记录显示的角度值（应该与竖屏时一致）

## 预期结果

### 成功标准
1. **角度一致性**：相同身体姿势在所有测试场景下计算出的角度差异应小于 ±2°
2. **状态检测准确性**：日志中正确显示画面旋转开启/关闭状态
3. **方向转换正确性**：重力向量根据选择的策略正确转换

### 日志验证
在 Xcode 控制台中查看以下关键日志：

#### 画面旋转关闭时：
```
🔒 画面旋转关闭，使用设备物理方向进行转换: 竖屏
📱 竖屏模式：重力向量转换
🎯 最终选择角度: 87.0° (重力向量夹角方法)
✅ 角度计算一致性良好: 差异 1.2°
```

#### 画面旋转开启时：
```
🔄 画面旋转开启，使用界面方向进行转换: 竖屏
📱 竖屏模式：重力向量转换
🎯 最终选择角度: 87.0° (重力向量夹角方法)
✅ 角度计算一致性良好: 差异 0.8°
```

## 问题排查

### 常见问题

#### 1. 角度差异过大（>5°）
**可能原因**：
- 身体姿势在测试过程中发生变化
- 设备方向检测不准确
- 重力向量转换逻辑错误

**解决方法**：
- 确保在整个测试过程中保持相同姿势
- 检查设备是否正确识别方向变化
- 查看日志中的重力向量转换过程

#### 2. 画面旋转状态检测错误
**可能原因**：
- 应用不支持某个方向的旋转
- 系统旋转锁定状态检测失败

**解决方法**：
- 检查应用的支持方向配置
- 重新设置旋转锁定状态

#### 3. 重力向量异常
**可能原因**：
- 设备传感器故障
- CoreMotion 权限问题

**解决方法**：
- 重启应用
- 检查设备运动权限
- 尝试其他设备

## 测试报告

### 自动生成报告
应用内置了 `RotationTestHelper` 工具，会自动记录测试结果并生成报告。

### 手动记录模板
```
测试日期：____年____月____日
测试设备：________________
测试人员：________________

画面旋转关闭状态：
- 竖屏角度：____°
- 左横屏角度：____°
- 右横屏角度：____°
- 最大差异：____°

画面旋转开启状态：
- 竖屏角度：____°
- 左横屏角度：____°
- 右横屏角度：____°
- 最大差异：____°

总体评价：
□ 通过（差异 ≤ 2°）
□ 基本通过（差异 ≤ 5°）
□ 未通过（差异 > 5°）

备注：
_________________________________
```

## 注意事项

1. **测试顺序**：建议先测试画面旋转关闭状态，再测试开启状态
2. **姿势保持**：整个测试过程中尽量保持相同的身体姿势
3. **等待时间**：设备旋转后等待 1-2 秒，确保传感器数据稳定
4. **多次测试**：每个场景建议测试 2-3 次，确保结果稳定
5. **日志记录**：保存完整的 Xcode 控制台日志用于问题分析
