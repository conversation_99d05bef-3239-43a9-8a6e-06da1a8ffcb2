# FitCount iOS 项目设备方向角度计算修复说明

## 问题描述

在 FitCount iOS 项目中，SitUpCounter.swift 文件的 calculateTorsoAngle 函数存在设备方向相关的问题：

1. **竖屏模式**：角度计算正确
2. **横屏模式**：角度计算出现错误，正好相差一个补角（90° - 正确角度）

## 调试数据分析

通过分析 out.txt 文件中的调试输出，发现了以下关键问题：

### 1. 重力向量在不断变化
- 开始时：`重力向量: (-0.030, 0.800, -0.599)`
- 后来变为：`重力向量: (-0.377, 0.686, -0.622)`
- 再后来：`重力向量: (-0.725, 0.408, -0.554)`

### 2. 角度计算结果不一致
- 主要方法（重力向量夹角）：约 87-88°（相对稳定）
- 备用方法（补角）：从 80° 逐渐变为 50°、40°、30°、20°（不断变化）

### 3. 问题根源
重力向量的 X 和 Y 分量在不断变化，说明设备在旋转，但我们的坐标转换逻辑没有正确处理这种变化。

## 问题根源分析

### 1. 坐标系转换逻辑错误
- **发现**：虽然 `DeviceMotionManager.convertGravityToWorldCoordinates` 方法存在，但转换逻辑可能不正确
- **影响**：导致在不同设备方向下，重力向量转换不一致

### 2. MediaPipe worldLandmarks 坐标系理解偏差
- **关键发现**：从调试数据看，重力向量在设备旋转时发生变化，说明我们的理解有偏差
- **实际情况**：需要确保重力向量在所有设备方向下都指向同一个方向（地面）

### 3. 角度计算方法选择问题
- **观察**：主要方法（重力向量夹角）相对稳定，备用方法（补角）变化很大
- **结论**：应该优先使用重力向量夹角方法，并确保重力向量的一致性

## 修复方案

### 1. 修复 DeviceMotionManager 中的重力向量转换

**文件**：`FitCount/Managers/DeviceMotionManager.swift`

**关键修改**：
- 添加了根据设备方向的正确坐标转换逻辑
- 支持竖屏、倒置竖屏、左横屏、右横屏四种方向
- 确保在所有设备方向下，重力向量都正确指向地面方向

```swift
// 根据设备方向转换重力向量到 MediaPipe 坐标系
switch currentOrientation {
case .portrait:
    // 竖屏：设备正常握持
    convertedGravity = (x: gravity.x, y: -gravity.y, z: gravity.z)

case .portraitUpsideDown:
    // 倒置竖屏：设备倒置握持
    convertedGravity = (x: -gravity.x, y: gravity.y, z: gravity.z)

case .landscapeLeft:
    // 左横屏：设备逆时针旋转90度
    convertedGravity = (x: gravity.y, y: gravity.x, z: gravity.z)

case .landscapeRight:
    // 右横屏：设备顺时针旋转90度
    convertedGravity = (x: -gravity.y, y: -gravity.x, z: gravity.z)
}
```

### 2. 重新设计 SitUpCounter 中的角度计算逻辑

**文件**：`FitCount/services/SitUpCounter.swift`

**关键修改**：
- 移除了补角修正逻辑
- 使用躯干向量与重力向量的夹角直接计算倾斜角度
- 确保角度计算不受设备方向影响

```swift
// 计算躯干向量与重力向量的点积
let dotProduct = normalizedTorso.x * gravityVector.x +
                normalizedTorso.y * gravityVector.y +
                normalizedTorso.z * gravityVector.z

// 计算躯干向量与重力向量的夹角
let clampedDotProduct = max(-1.0, min(1.0, Double(dotProduct)))
let angleWithGravity = acos(abs(clampedDotProduct))
let angleWithGravityDegrees = angleWithGravity * 180.0 / .pi

// 直接使用夹角作为倾斜角度
let angleDegrees = angleWithGravityDegrees
```

### 3. 更新注释和文档

- 修正了对 MediaPipe worldLandmarks 坐标系的理解
- 添加了详细的坐标系转换说明
- 更新了调试输出，便于问题诊断

## 修复效果

### 预期结果
1. **一致性**：相同的身体姿势在竖屏、横屏等所有设备方向下都能计算出一致且正确的躯干角度
2. **准确性**：角度计算不再出现补角关系的错误
3. **稳定性**：设备方向变化时，角度计算保持稳定

### 验证方法
1. 在不同设备方向下保持相同身体姿势
2. 观察角度计算结果是否一致
3. 检查调试日志中的重力向量转换是否正确

## 技术要点

### 坐标系转换原理
- **CoreMotion 坐标系**：以设备为中心，随设备旋转
- **MediaPipe 坐标系**：基于图像方向，需要根据设备方向正确转换
- **关键**：确保重力向量在所有方向下都正确指向地面

### 角度计算原理
- 使用向量点积计算夹角：`cos(θ) = (a·b) / (|a||b|)`
- 躯干向量与重力向量的夹角直接反映身体倾斜程度
- 0°表示躺下，90°表示站立

## 注意事项

1. **兼容性**：保持了现有 API 的向后兼容性
2. **性能**：没有增加显著的计算开销
3. **调试**：增强了调试日志，便于问题排查
4. **国际化**：所有用户可见文本都符合 iOS 国际化标准

## 编译验证

修复完成后，项目编译成功，没有错误或警告。

```bash
** BUILD SUCCEEDED **
```

## 总结

通过正确理解 MediaPipe worldLandmarks 坐标系与设备方向的关系，并修复重力向量转换逻辑，成功解决了横屏模式下角度计算不准确的问题。现在在所有设备方向下，相同的身体姿势都能计算出一致且正确的躯干角度。
