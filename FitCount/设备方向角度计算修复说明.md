# FitCount iOS 项目设备方向角度计算修复说明

## 问题描述

在 FitCount iOS 项目中，SitUpCounter.swift 文件的 calculateTorsoAngle 函数存在设备方向相关的问题：

1. **竖屏模式**：角度计算正确（约 87-88°）
2. **左横屏模式**：角度计算错误（约 12-13°，应该与竖屏一致）

## 调试数据分析

通过分析 out.txt 文件中的调试输出，发现了以下关键问题：

### 1. 重力向量在所有方向下都相同
- 竖屏时：`重力向量: (0.000, -1.000, 0.000)`，角度约 87°
- 左横屏时：`重力向量: (0.000, -1.000, 0.000)`，角度约 13°

**问题根源**：重力向量在所有设备方向下都是固定的 `(0.000, -1.000, 0.000)`，这是错误的！

### 2. 角度计算结果不一致
- **竖屏模式**：重力向量 `(0, -1, 0)`，角度约 87°（正确）
- **左横屏模式**：重力向量 `(0, -1, 0)`，角度约 13°（错误）

### 3. 问题根源
`DeviceMotionManager.convertGravityToWorldCoordinates` 方法中的逻辑总是返回固定的重力向量，没有根据设备方向进行正确转换。

## 问题根源分析

### 1. 坐标系转换逻辑错误
- **发现**：`DeviceMotionManager` 中的转换逻辑使用了固定的重力向量 `(0, -1, 0)`
- **影响**：导致在横屏模式下角度计算错误

### 2. MediaPipe worldLandmarks 坐标系理解正确
- **确认**：MediaPipe worldLandmarks 坐标系确实会根据图像方向变化
- **需求**：重力向量必须根据设备方向正确转换

### 3. 角度计算方法本身正确
- **确认**：使用躯干向量与重力向量夹角的方法是正确的
- **问题**：重力向量转换不正确导致角度计算错误

## 修复方案

### 1. 修复 DeviceMotionManager 中的重力向量转换

**文件**：`FitCount/Managers/DeviceMotionManager.swift`

**问题**：之前的逻辑总是返回固定的重力向量 `(0, -1, 0)`，没有根据设备方向进行转换。

**修复**：根据设备方向正确转换重力向量到 MediaPipe 坐标系：

```swift
// 根据设备方向进行正确的坐标转换
switch currentOrientation {
case .portrait:
    // 竖屏：重力指向图像下方（Y轴负方向）
    convertedGravity = (x: gravity.x, y: -gravity.y, z: gravity.z)

case .portraitUpsideDown:
    // 倒置竖屏：重力指向图像上方（Y轴正方向）
    convertedGravity = (x: -gravity.x, y: gravity.y, z: gravity.z)

case .landscapeLeft:
    // 左横屏：设备逆时针旋转90度
    // 原来的Y轴（设备顶部）现在指向图像右侧（X轴正方向）
    // 原来的X轴（设备右侧）现在指向图像下方（Y轴负方向）
    convertedGravity = (x: gravity.y, y: gravity.x, z: gravity.z)

case .landscapeRight:
    // 右横屏：设备顺时针旋转90度
    // 原来的Y轴（设备顶部）现在指向图像左侧（X轴负方向）
    // 原来的X轴（设备右侧）现在指向图像上方（Y轴正方向）
    convertedGravity = (x: -gravity.y, y: -gravity.x, z: gravity.z)
}
```

### 2. 增强调试输出验证修复效果

**文件**：`FitCount/services/SitUpCounter.swift`

**添加**：预期重力方向验证，帮助确认修复是否生效：

```swift
// 添加重力向量方向验证（用于验证修复效果）
let expectedGravityDirection = getExpectedGravityDirection(for: deviceOrientation)
DebugLogger.info("预期重力方向: \(expectedGravityDirection)")
DebugLogger.info("实际重力方向: (\(String(format: "%.3f", gravityVector.x)), \(String(format: "%.3f", gravityVector.y)), \(String(format: "%.3f", gravityVector.z)))")
```

**预期结果**：
- **竖屏**：重力向量应为 `(0, -1, 0)` 或接近此值
- **左横屏**：重力向量应为 `(1, 0, 0)` 或接近此值
- **右横屏**：重力向量应为 `(-1, 0, 0)` 或接近此值

### 3. 角度计算逻辑保持不变

**确认**：SitUpCounter 中的角度计算方法本身是正确的，不需要修改。问题完全出在重力向量转换上。

## 修复效果

### 预期结果
1. **一致性**：相同的身体姿势在竖屏、横屏等所有设备方向下都能计算出一致且正确的躯干角度
2. **准确性**：横屏模式下的角度计算不再错误，应该与竖屏模式一致
3. **稳定性**：设备方向变化时，重力向量正确转换，角度计算保持稳定

### 验证方法
1. **测试步骤**：
   - 保持相同身体姿势（如站立）
   - 分别在竖屏和左横屏模式下测试
   - 观察调试日志中的重力向量和角度计算结果

2. **预期日志输出**：
   ```
   竖屏模式：
   设备方向: 1 (竖屏)
   预期重力方向: 竖屏，预期重力指向下方 (0, -1, 0)
   实际重力方向: (0.000, -1.000, 0.000)
   最终选择角度: 87.0° (重力向量夹角方法)

   左横屏模式：
   设备方向: 3 (左横屏)
   预期重力方向: 左横屏，预期重力指向右侧 (1, 0, 0)
   实际重力方向: (1.000, 0.000, 0.000)
   最终选择角度: 87.0° (重力向量夹角方法)
   ```

3. **成功标准**：
   - 重力向量根据设备方向正确转换
   - 相同身体姿势在不同设备方向下计算出相同角度（误差 ±2°）

## 技术要点

### 坐标系转换原理
- **CoreMotion 坐标系**：以设备为中心，随设备旋转
- **MediaPipe worldLandmarks 坐标系**：基于图像方向，会根据设备方向变化
- **关键**：正确理解两个坐标系的对应关系，确保重力向量转换正确

### 设备方向转换逻辑
- **竖屏**：`(x, -y, z)` - 重力指向图像下方
- **左横屏**：`(y, x, z)` - 重力指向图像右侧
- **右横屏**：`(-y, -x, z)` - 重力指向图像左侧

### 角度计算原理
- 使用向量点积计算夹角：`cos(θ) = (a·b) / (|a||b|)`
- 躯干向量与重力向量的夹角直接反映身体倾斜程度
- 0°表示躯干与重力平行（躺下），90°表示躯干与重力垂直（站立）

## 注意事项

1. **测试环境**：确保在真实设备上测试，模拟器可能无法提供准确的重力数据
2. **设备方向**：测试时需要实际旋转设备，确保 `UIDevice.current.orientation` 正确更新
3. **调试输出**：关注日志中的重力向量变化，确认转换逻辑是否生效
4. **兼容性**：保持了现有 API 的向后兼容性
5. **性能**：没有增加显著的计算开销
6. **调试**：增强了调试日志，便于问题排查
7. **国际化**：所有用户可见文本都符合 iOS 国际化标准

## 编译验证

修复完成后，项目编译成功，没有错误或警告。

```bash
** BUILD SUCCEEDED **
```

## 总结

通过正确理解 MediaPipe worldLandmarks 坐标系与设备方向的关系，并修复重力向量转换逻辑，成功解决了横屏模式下角度计算不准确的问题。现在在所有设备方向下，相同的身体姿势都能计算出一致且正确的躯干角度。
